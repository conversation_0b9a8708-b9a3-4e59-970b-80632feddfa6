package com.example.myfirstapplication.gamepad

import android.view.InputDevice
import android.view.KeyEvent
import android.view.MotionEvent
import com.example.myfirstapplication.data.model.ControlCommand
import com.example.myfirstapplication.data.model.CommandType
import com.example.myfirstapplication.data.model.GamepadInput
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * 手柄管理器
 */
class GamepadManager {
    
    private val _gamepadInput = MutableStateFlow(GamepadInput())
    val gamepadInput: StateFlow<GamepadInput> = _gamepadInput.asStateFlow()
    
    private val _isGamepadConnected = MutableStateFlow(false)
    val isGamepadConnected: StateFlow<Boolean> = _isGamepadConnected.asStateFlow()
    
    private val _connectedGamepads = MutableStateFlow<List<InputDevice>>(emptyList())
    val connectedGamepads: StateFlow<List<InputDevice>> = _connectedGamepads.asStateFlow()
    
    // 死区阈值
    private val deadZone = 0.1f
    
    /**
     * 检查设备是否为游戏手柄
     */
    private fun isGamepad(device: InputDevice): Boolean {
        val sources = device.sources
        return (sources and InputDevice.SOURCE_GAMEPAD) == InputDevice.SOURCE_GAMEPAD ||
               (sources and InputDevice.SOURCE_JOYSTICK) == InputDevice.SOURCE_JOYSTICK
    }
    
    /**
     * 扫描连接的游戏手柄
     */
    fun scanForGamepads() {
        val deviceIds = InputDevice.getDeviceIds()
        val gamepads = mutableListOf<InputDevice>()
        
        for (deviceId in deviceIds) {
            val device = InputDevice.getDevice(deviceId)
            if (device != null && isGamepad(device)) {
                gamepads.add(device)
            }
        }
        
        _connectedGamepads.value = gamepads
        _isGamepadConnected.value = gamepads.isNotEmpty()
    }
    
    /**
     * 处理手柄按键事件
     */
    fun handleKeyEvent(event: KeyEvent): Boolean {
        val device = InputDevice.getDevice(event.deviceId)
        if (device == null || !isGamepad(device)) {
            return false
        }
        
        val isPressed = event.action == KeyEvent.ACTION_DOWN
        val currentInput = _gamepadInput.value
        
        val newInput = when (event.keyCode) {
            KeyEvent.KEYCODE_BUTTON_A -> currentInput.copy(buttonA = isPressed)
            KeyEvent.KEYCODE_BUTTON_B -> currentInput.copy(buttonB = isPressed)
            KeyEvent.KEYCODE_BUTTON_X -> currentInput.copy(buttonX = isPressed)
            KeyEvent.KEYCODE_BUTTON_Y -> currentInput.copy(buttonY = isPressed)
            KeyEvent.KEYCODE_BUTTON_START -> currentInput.copy(buttonStart = isPressed)
            KeyEvent.KEYCODE_BUTTON_SELECT -> currentInput.copy(buttonSelect = isPressed)
            KeyEvent.KEYCODE_DPAD_UP -> currentInput.copy(dPadUp = isPressed)
            KeyEvent.KEYCODE_DPAD_DOWN -> currentInput.copy(dPadDown = isPressed)
            KeyEvent.KEYCODE_DPAD_LEFT -> currentInput.copy(dPadLeft = isPressed)
            KeyEvent.KEYCODE_DPAD_RIGHT -> currentInput.copy(dPadRight = isPressed)
            else -> return false
        }
        
        _gamepadInput.value = newInput
        return true
    }
    
    /**
     * 处理手柄摇杆和扳机事件
     */
    fun handleMotionEvent(event: MotionEvent): Boolean {
        val device = InputDevice.getDevice(event.deviceId)
        if (device == null || !isGamepad(device)) {
            return false
        }
        
        val currentInput = _gamepadInput.value
        
        // 获取摇杆值
        val leftStickX = getCenteredAxis(event, MotionEvent.AXIS_X)
        val leftStickY = getCenteredAxis(event, MotionEvent.AXIS_Y)
        val rightStickX = getCenteredAxis(event, MotionEvent.AXIS_Z)
        val rightStickY = getCenteredAxis(event, MotionEvent.AXIS_RZ)
        
        // 获取扳机值
        val leftTrigger = event.getAxisValue(MotionEvent.AXIS_LTRIGGER)
        val rightTrigger = event.getAxisValue(MotionEvent.AXIS_RTRIGGER)
        
        val newInput = currentInput.copy(
            leftStickX = leftStickX,
            leftStickY = leftStickY,
            rightStickX = rightStickX,
            rightStickY = rightStickY,
            leftTrigger = leftTrigger,
            rightTrigger = rightTrigger
        )
        
        _gamepadInput.value = newInput
        return true
    }
    
    /**
     * 获取居中的轴值（应用死区）
     */
    private fun getCenteredAxis(event: MotionEvent, axis: Int): Float {
        val value = event.getAxisValue(axis)
        return if (kotlin.math.abs(value) > deadZone) value else 0f
    }
    
    /**
     * 将手柄输入转换为机器狗控制命令
     */
    fun gamepadInputToCommand(input: GamepadInput): ControlCommand? {
        // 移动控制（左摇杆）
        if (kotlin.math.abs(input.leftStickX) > deadZone || kotlin.math.abs(input.leftStickY) > deadZone) {
            return ControlCommand(
                type = CommandType.MOVE,
                x = input.leftStickX,
                y = -input.leftStickY, // Y轴反转
                speed = 0.5f
            )
        }
        
        // 转向控制（右摇杆X轴）
        if (kotlin.math.abs(input.rightStickX) > deadZone) {
            return ControlCommand(
                type = CommandType.TURN,
                x = input.rightStickX,
                speed = 0.5f
            )
        }
        
        // 按钮控制
        when {
            input.buttonA -> return ControlCommand(type = CommandType.SIT)
            input.buttonB -> return ControlCommand(type = CommandType.STAND)
            input.buttonX -> return ControlCommand(type = CommandType.LIE_DOWN)
            input.buttonY -> return ControlCommand(type = CommandType.DANCE)
            input.buttonStart -> return ControlCommand(type = CommandType.STOP)
            input.dPadUp -> return ControlCommand(
                type = CommandType.MOVE,
                y = 1f,
                speed = 0.3f
            )
            input.dPadDown -> return ControlCommand(
                type = CommandType.MOVE,
                y = -1f,
                speed = 0.3f
            )
            input.dPadLeft -> return ControlCommand(
                type = CommandType.TURN,
                x = -1f,
                speed = 0.3f
            )
            input.dPadRight -> return ControlCommand(
                type = CommandType.TURN,
                x = 1f,
                speed = 0.3f
            )
        }
        
        return null
    }
    
    /**
     * 获取当前手柄输入的控制命令
     */
    fun getCurrentCommand(): ControlCommand? {
        return gamepadInputToCommand(_gamepadInput.value)
    }
    
    /**
     * 重置手柄输入
     */
    fun resetInput() {
        _gamepadInput.value = GamepadInput()
    }
    
    /**
     * 设置死区阈值
     */
    fun setDeadZone(threshold: Float) {
        // deadZone = threshold.coerceIn(0f, 1f)
    }
    
    /**
     * 获取手柄信息
     */
    fun getGamepadInfo(deviceId: Int): String? {
        val device = InputDevice.getDevice(deviceId)
        return if (device != null && isGamepad(device)) {
            "Name: ${device.name}\nVendor: ${device.vendorId}\nProduct: ${device.productId}"
        } else {
            null
        }
    }
    
    /**
     * 检查特定手柄是否连接
     */
    fun isGamepadConnected(deviceId: Int): Boolean {
        val device = InputDevice.getDevice(deviceId)
        return device != null && isGamepad(device)
    }
}
