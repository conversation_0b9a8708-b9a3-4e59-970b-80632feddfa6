{"logs": [{"outputFile": "com.example.myfirstapplication.app-mergeDebugResources-61:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ffaf60804bb777caaab07cd66703845d\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,61,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3028,3082,3128,3174,3216,3256,3303,3339,3429,3541,3652", "endLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63,67", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3023,3077,3123,3169,3211,3251,3298,3334,3424,3536,3647,3842"}, "to": {"startLines": "77,78,79,81,82,109,121,122,123,124,125,126,127,189,190,191,192,193,194,195,196,198,199,200,203,219,222", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4797,4871,4929,5050,5101,6508,7356,7421,7475,7541,7642,7700,7752,12253,12315,12369,12419,12473,12519,12565,12607,12718,12765,12801,13002,13982,14093", "endLines": "77,78,79,81,82,109,121,122,123,124,125,126,127,189,190,191,192,193,194,195,196,198,199,200,205,221,225", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "4866,4924,4979,5096,5151,6556,7416,7470,7536,7637,7695,7747,7807,12310,12364,12414,12468,12514,12560,12602,12642,12760,12796,12886,13109,14088,14283"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\eb80c39fd83dc09184ca43a2c381fdcb\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "107", "startColumns": "4", "startOffsets": "6404", "endColumns": "53", "endOffsets": "6453"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ee81e9003baef06be7850ff4f00325b8\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "2,7,8,10,11,12,13,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,84,85,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,111,114,115,116,117,118,119,120,197,226,227,231,232,236,238,239,247,253,263,296,317,350", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,470,542,672,737,803,872,1208,1278,1346,1418,1488,1549,1623,1696,1757,1818,1880,1944,2006,2067,2135,2235,2295,2361,2434,2503,2560,2612,2674,2746,2822,2887,2946,3005,3065,3125,3185,3245,3305,3365,3425,3485,3545,3605,3664,3724,3784,3844,3904,3964,4024,4084,4144,4204,4264,4323,4383,4443,4502,4561,4620,4679,4738,5210,5245,5442,5497,5560,5615,5673,5731,5792,5855,5912,5963,6013,6074,6131,6197,6231,6266,6634,6845,6912,6984,7053,7122,7196,7268,12647,14288,14405,14606,14716,14917,15141,15213,15584,15787,16088,17819,18500,19182", "endLines": "2,7,8,10,11,12,13,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,84,85,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,111,114,115,116,117,118,119,120,197,226,230,231,235,236,238,239,252,262,295,316,349,355", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "205,537,625,732,798,867,930,1273,1341,1413,1483,1544,1618,1691,1752,1813,1875,1939,2001,2062,2130,2230,2290,2356,2429,2498,2555,2607,2669,2741,2817,2882,2941,3000,3060,3120,3180,3240,3300,3360,3420,3480,3540,3600,3659,3719,3779,3839,3899,3959,4019,4079,4139,4199,4259,4318,4378,4438,4497,4556,4615,4674,4733,4792,5240,5275,5492,5555,5610,5668,5726,5787,5850,5907,5958,6008,6069,6126,6192,6226,6261,6296,6699,6907,6979,7048,7117,7191,7263,7351,12713,14400,14601,14711,14912,15041,15208,15275,15782,16083,17814,18495,19177,19344"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8b176e8aba06099230d4846cfebebfa3\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,15,16,17,18,19,20,21,22,23,24,25,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,173,261,347,428,512,581,646,729,835,921,1041,1095,1164,1225,1294,1383,1478,1552,1649,1742,1840,1989,2080,2168,2264,2362,2426,2494,2581,2675,2742,2814,2886,2987,3096,3172,3241,3289,3355,3419,3493,3550,3607,3679,3729,3783,3854,3925,3995,4064,4122,4198,4269,4343,4429,4479,4549,4614,5329", "endLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,73,76", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "168,256,342,423,507,576,641,724,830,916,1036,1090,1159,1220,1289,1378,1473,1547,1644,1737,1835,1984,2075,2163,2259,2357,2421,2489,2576,2670,2737,2809,2881,2982,3091,3167,3236,3284,3350,3414,3488,3545,3602,3674,3724,3778,3849,3920,3990,4059,4117,4193,4264,4338,4424,4474,4544,4609,5324,5477"}, "to": {"startLines": "110,128,129,130,131,132,133,134,135,136,137,140,141,142,143,144,145,146,147,148,149,150,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,206,216", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6561,7812,7900,7986,8067,8151,8220,8285,8368,8474,8560,8680,8734,8803,8864,8933,9022,9117,9191,9288,9381,9479,9628,9719,9807,9903,10001,10065,10133,10220,10314,10381,10453,10525,10626,10735,10811,10880,10928,10994,11058,11132,11189,11246,11318,11368,11422,11493,11564,11634,11703,11761,11837,11908,11982,12068,12118,12188,13114,13829", "endLines": "110,128,129,130,131,132,133,134,135,136,139,140,141,142,143,144,145,146,147,148,149,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,215,218", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "6629,7895,7981,8062,8146,8215,8280,8363,8469,8555,8675,8729,8798,8859,8928,9017,9112,9186,9283,9376,9474,9623,9714,9802,9898,9996,10060,10128,10215,10309,10376,10448,10520,10621,10730,10806,10875,10923,10989,11053,11127,11184,11241,11313,11363,11417,11488,11559,11629,11698,11756,11832,11903,11977,12063,12113,12183,12248,13824,13977"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fce910b49377672473080ebeba046646\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "201,202", "startColumns": "4,4", "startOffsets": "12891,12947", "endColumns": "55,54", "endOffsets": "12942,12997"}}, {"source": "D:\\Users\\Administrator\\AndroidStudioProjects\\MyfirstApplication\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "56", "endColumns": "95", "endOffsets": "147"}, "to": {"startLines": "237", "startColumns": "4", "startOffsets": "15046", "endColumns": "94", "endOffsets": "15136"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\09a3a233c8f6de7d440dffb5c239312f\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "83,87", "startColumns": "4,4", "startOffsets": "5156,5333", "endColumns": "53,66", "endOffsets": "5205,5395"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b9ff2cc660253f8dbb77cb0cbe74e61c\\transformed\\work-runtime-2.9.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,120,190,254", "endColumns": "64,69,63,60", "endOffsets": "115,185,249,310"}, "to": {"startLines": "3,4,5,6", "startColumns": "4,4,4,4", "startOffsets": "210,275,345,409", "endColumns": "64,69,63,60", "endOffsets": "270,340,404,465"}}, {"source": "D:\\Users\\Administrator\\AndroidStudioProjects\\MyfirstApplication\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "7,2,3,4,5,6,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "286,55,102,149,196,241,328", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "323,97,144,191,236,281,365"}, "to": {"startLines": "9,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "630,935,982,1029,1076,1121,1166", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "667,977,1024,1071,1116,1161,1203"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fd1fb44d3025048634a00a9f3a07ea5c\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "80", "startColumns": "4", "startOffsets": "4984", "endColumns": "65", "endOffsets": "5045"}}, {"source": "D:\\Users\\Administrator\\AndroidStudioProjects\\MyfirstApplication\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1", "startColumns": "4", "startOffsets": "16", "endColumns": "57", "endOffsets": "69"}, "to": {"startLines": "113", "startColumns": "4", "startOffsets": "6787", "endColumns": "57", "endOffsets": "6840"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0b77f7f969be2bb725302f33ce1d15eb\\transformed\\activity-1.8.2\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "88,106", "startColumns": "4,4", "startOffsets": "5400,6344", "endColumns": "41,59", "endOffsets": "5437,6399"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ecea0ab19d2e934d014664cfa979d2d5\\transformed\\navigation-common-2.7.6\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "356,369,375,381,390", "startColumns": "4,4,4,4,4", "startOffsets": "19349,19988,20232,20479,20842", "endLines": "368,374,380,383,394", "endColumns": "24,24,24,24,24", "endOffsets": "19983,20227,20474,20607,21019"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3684f126870460cbf44e1e8c4b7b80a6\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "112", "startColumns": "4", "startOffsets": "6704", "endColumns": "82", "endOffsets": "6782"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\42a471aecd7a7bceb9a1136b70ac9fd2\\transformed\\navigation-runtime-2.7.6\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "86,240,384,387", "startColumns": "4,4,4,4", "startOffsets": "5280,15280,20612,20727", "endLines": "86,246,386,389", "endColumns": "52,24,24,24", "endOffsets": "5328,15579,20722,20837"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fe94774b27fdaa35093098e8a904795c\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "108", "startColumns": "4", "startOffsets": "6458", "endColumns": "49", "endOffsets": "6503"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b53b0a6e7beae069ef1b2e945452fd0d\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "105", "startColumns": "4", "startOffsets": "6301", "endColumns": "42", "endOffsets": "6339"}}]}]}