package com.example.myfirstapplication.wifi

import android.content.Context
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkRequest
import android.net.wifi.ScanResult
import android.net.wifi.WifiConfiguration
import android.net.wifi.WifiManager
import android.net.wifi.WifiNetworkSpecifier
import android.os.Build
import androidx.annotation.RequiresApi
import com.example.myfirstapplication.data.model.WiFiNetwork
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * WiFi连接状态
 */
enum class WiFiConnectionStatus {
    DISCONNECTED,
    SCANNING,
    CONNECTING,
    CONNECTED,
    FAILED
}

/**
 * WiFi管理器
 */
class WiFiManager(private val context: Context) {
    
    private val wifiManager = context.applicationContext.getSystemService(Context.WIFI_SERVICE) as WifiManager
    private val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
    
    private val _connectionStatus = MutableStateFlow(WiFiConnectionStatus.DISCONNECTED)
    val connectionStatus: StateFlow<WiFiConnectionStatus> = _connectionStatus.asStateFlow()
    
    private val _availableNetworks = MutableStateFlow<List<WiFiNetwork>>(emptyList())
    val availableNetworks: StateFlow<List<WiFiNetwork>> = _availableNetworks.asStateFlow()
    
    private val _currentNetwork = MutableStateFlow<WiFiNetwork?>(null)
    val currentNetwork: StateFlow<WiFiNetwork?> = _currentNetwork.asStateFlow()
    
    private var networkCallback: ConnectivityManager.NetworkCallback? = null
    
    /**
     * 检查WiFi是否启用
     */
    fun isWiFiEnabled(): Boolean {
        return wifiManager.isWifiEnabled
    }
    
    /**
     * 启用WiFi
     */
    fun enableWiFi(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            // Android 10+ 不允许应用直接启用WiFi
            false
        } else {
            @Suppress("DEPRECATION")
            wifiManager.setWifiEnabled(true)
        }
    }
    
    /**
     * 扫描WiFi网络
     */
    fun scanNetworks(): Boolean {
        if (!isWiFiEnabled()) {
            return false
        }
        
        _connectionStatus.value = WiFiConnectionStatus.SCANNING
        return wifiManager.startScan()
    }
    
    /**
     * 获取扫描结果
     */
    fun getScanResults(): List<WiFiNetwork> {
        if (!isWiFiEnabled()) {
            return emptyList()
        }
        
        val scanResults = wifiManager.scanResults
        val networks = scanResults.map { scanResult ->
            WiFiNetwork(
                ssid = scanResult.SSID,
                bssid = scanResult.BSSID,
                signalStrength = WifiManager.calculateSignalLevel(scanResult.level, 5),
                isSecured = scanResult.capabilities.contains("WPA") || 
                           scanResult.capabilities.contains("WEP") ||
                           scanResult.capabilities.contains("PSK"),
                frequency = scanResult.frequency
            )
        }.distinctBy { it.ssid }
        
        _availableNetworks.value = networks
        return networks
    }
    
    /**
     * 连接到WiFi网络 (Android 10+)
     */
    @RequiresApi(Build.VERSION_CODES.Q)
    fun connectToNetworkModern(ssid: String, password: String): Boolean {
        try {
            _connectionStatus.value = WiFiConnectionStatus.CONNECTING
            
            val specifier = WifiNetworkSpecifier.Builder()
                .setSsid(ssid)
                .setWpa2Passphrase(password)
                .build()
            
            val request = NetworkRequest.Builder()
                .addTransportType(NetworkCapabilities.TRANSPORT_WIFI)
                .removeCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
                .setNetworkSpecifier(specifier)
                .build()
            
            networkCallback = object : ConnectivityManager.NetworkCallback() {
                override fun onAvailable(network: Network) {
                    super.onAvailable(network)
                    _connectionStatus.value = WiFiConnectionStatus.CONNECTED
                    updateCurrentNetwork()
                }
                
                override fun onUnavailable() {
                    super.onUnavailable()
                    _connectionStatus.value = WiFiConnectionStatus.FAILED
                }
                
                override fun onLost(network: Network) {
                    super.onLost(network)
                    _connectionStatus.value = WiFiConnectionStatus.DISCONNECTED
                    _currentNetwork.value = null
                }
            }
            
            connectivityManager.requestNetwork(request, networkCallback!!)
            return true
        } catch (e: Exception) {
            _connectionStatus.value = WiFiConnectionStatus.FAILED
            return false
        }
    }
    
    /**
     * 连接到WiFi网络 (Android 9及以下)
     */
    @Suppress("DEPRECATION")
    fun connectToNetworkLegacy(ssid: String, password: String): Boolean {
        try {
            _connectionStatus.value = WiFiConnectionStatus.CONNECTING
            
            val wifiConfig = WifiConfiguration().apply {
                SSID = "\"$ssid\""
                preSharedKey = "\"$password\""
                allowedKeyManagement.set(WifiConfiguration.KeyMgmt.WPA_PSK)
            }
            
            val networkId = wifiManager.addNetwork(wifiConfig)
            if (networkId == -1) {
                _connectionStatus.value = WiFiConnectionStatus.FAILED
                return false
            }
            
            val success = wifiManager.enableNetwork(networkId, true)
            if (success) {
                _connectionStatus.value = WiFiConnectionStatus.CONNECTED
                updateCurrentNetwork()
            } else {
                _connectionStatus.value = WiFiConnectionStatus.FAILED
            }
            
            return success
        } catch (e: Exception) {
            _connectionStatus.value = WiFiConnectionStatus.FAILED
            return false
        }
    }
    
    /**
     * 连接到WiFi网络
     */
    fun connectToNetwork(ssid: String, password: String): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            connectToNetworkModern(ssid, password)
        } else {
            connectToNetworkLegacy(ssid, password)
        }
    }
    
    /**
     * 断开WiFi连接
     */
    fun disconnect() {
        networkCallback?.let { callback ->
            connectivityManager.unregisterNetworkCallback(callback)
            networkCallback = null
        }
        _connectionStatus.value = WiFiConnectionStatus.DISCONNECTED
        _currentNetwork.value = null
    }
    
    /**
     * 获取当前连接的网络信息
     */
    private fun updateCurrentNetwork() {
        val wifiInfo = wifiManager.connectionInfo
        if (wifiInfo != null && wifiInfo.ssid != "<unknown ssid>") {
            val network = WiFiNetwork(
                ssid = wifiInfo.ssid.removeSurrounding("\""),
                bssid = wifiInfo.bssid ?: "",
                signalStrength = WifiManager.calculateSignalLevel(wifiInfo.rssi, 5),
                isSecured = true, // 假设已连接的网络是安全的
                frequency = wifiInfo.frequency
            )
            _currentNetwork.value = network
        }
    }
    
    /**
     * 获取当前网络信息
     */
    fun getCurrentNetworkInfo(): WiFiNetwork? {
        updateCurrentNetwork()
        return _currentNetwork.value
    }
    
    /**
     * 检查是否连接到指定SSID
     */
    fun isConnectedToNetwork(ssid: String): Boolean {
        val currentNetwork = getCurrentNetworkInfo()
        return currentNetwork?.ssid == ssid
    }
    
    /**
     * 获取信号强度
     */
    fun getSignalStrength(): Int {
        val wifiInfo = wifiManager.connectionInfo
        return WifiManager.calculateSignalLevel(wifiInfo.rssi, 5)
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        disconnect()
    }
}
