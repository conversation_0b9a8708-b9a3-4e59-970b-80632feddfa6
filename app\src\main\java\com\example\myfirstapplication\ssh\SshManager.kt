package com.example.myfirstapplication.ssh

import com.jcraft.jsch.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.ByteArrayOutputStream
import java.io.InputStream
import java.util.*

/**
 * SSH连接管理器
 */
class SshManager {
    
    private var session: Session? = null
    private var jsch: JSch = JSch()
    
    /**
     * SSH连接结果
     */
    sealed class SshResult {
        data class Success(val output: String) : SshResult()
        data class Error(val message: String) : SshResult()
    }
    
    /**
     * 连接到SSH服务器
     */
    suspend fun connect(
        host: String,
        port: Int = 22,
        username: String,
        password: String,
        timeout: Int = 10000
    ): Boolean = withContext(Dispatchers.IO) {
        try {
            // 断开现有连接
            disconnect()
            
            // 创建新会话
            session = jsch.getSession(username, host, port)
            session?.setPassword(password)
            
            // 配置SSH
            val config = Properties()
            config["StrictHostKeyChecking"] = "no"
            config["PreferredAuthentications"] = "password"
            session?.setConfig(config)
            
            // 连接
            session?.connect(timeout)
            session?.isConnected == true
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }
    
    /**
     * 执行SSH命令
     */
    suspend fun executeCommand(command: String): SshResult = withContext(Dispatchers.IO) {
        try {
            if (session?.isConnected != true) {
                return@withContext SshResult.Error("SSH session not connected")
            }
            
            val channel = session!!.openChannel("exec") as ChannelExec
            channel.setCommand(command)
            
            val outputStream = ByteArrayOutputStream()
            val errorStream = ByteArrayOutputStream()
            
            channel.outputStream = outputStream
            channel.setErrStream(errorStream)
            
            channel.connect()
            
            // 等待命令执行完成
            while (!channel.isClosed) {
                Thread.sleep(100)
            }
            
            val output = outputStream.toString("UTF-8")
            val error = errorStream.toString("UTF-8")
            
            channel.disconnect()
            
            if (error.isNotEmpty()) {
                SshResult.Error(error)
            } else {
                SshResult.Success(output)
            }
        } catch (e: Exception) {
            SshResult.Error("Command execution failed: ${e.message}")
        }
    }
    
    /**
     * 创建交互式Shell
     */
    suspend fun createShell(): SshShell? = withContext(Dispatchers.IO) {
        try {
            if (session?.isConnected != true) {
                return@withContext null
            }
            
            val channel = session!!.openChannel("shell") as ChannelShell
            SshShell(channel)
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * 断开SSH连接
     */
    fun disconnect() {
        try {
            session?.disconnect()
        } catch (e: Exception) {
            // 忽略断开异常
        } finally {
            session = null
        }
    }
    
    /**
     * 检查连接状态
     */
    fun isConnected(): Boolean {
        return session?.isConnected == true
    }
    
    /**
     * 上传文件
     */
    suspend fun uploadFile(
        localPath: String,
        remotePath: String
    ): Boolean = withContext(Dispatchers.IO) {
        try {
            if (session?.isConnected != true) {
                return@withContext false
            }
            
            val channel = session!!.openChannel("sftp") as ChannelSftp
            channel.connect()
            
            channel.put(localPath, remotePath)
            channel.disconnect()
            
            true
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 下载文件
     */
    suspend fun downloadFile(
        remotePath: String,
        localPath: String
    ): Boolean = withContext(Dispatchers.IO) {
        try {
            if (session?.isConnected != true) {
                return@withContext false
            }
            
            val channel = session!!.openChannel("sftp") as ChannelSftp
            channel.connect()
            
            channel.get(remotePath, localPath)
            channel.disconnect()
            
            true
        } catch (e: Exception) {
            false
        }
    }
}

/**
 * SSH Shell包装类
 */
class SshShell(private val channel: ChannelShell) {
    
    private var isConnected = false
    
    /**
     * 连接Shell
     */
    suspend fun connect(): Boolean = withContext(Dispatchers.IO) {
        try {
            channel.connect()
            isConnected = true
            true
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 发送命令
     */
    suspend fun sendCommand(command: String): Boolean = withContext(Dispatchers.IO) {
        try {
            if (!isConnected) return@withContext false
            
            val outputStream = channel.outputStream
            outputStream.write("$command\n".toByteArray())
            outputStream.flush()
            true
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 读取输出
     */
    suspend fun readOutput(): String = withContext(Dispatchers.IO) {
        try {
            if (!isConnected) return@withContext ""
            
            val inputStream = channel.inputStream
            val buffer = ByteArray(1024)
            val output = StringBuilder()
            
            while (inputStream.available() > 0) {
                val bytesRead = inputStream.read(buffer)
                if (bytesRead > 0) {
                    output.append(String(buffer, 0, bytesRead))
                }
            }
            
            output.toString()
        } catch (e: Exception) {
            ""
        }
    }
    
    /**
     * 断开Shell连接
     */
    fun disconnect() {
        try {
            channel.disconnect()
        } catch (e: Exception) {
            // 忽略断开异常
        } finally {
            isConnected = false
        }
    }
    
    /**
     * 检查连接状态
     */
    fun isConnected(): Boolean {
        return isConnected && channel.isConnected
    }
}
