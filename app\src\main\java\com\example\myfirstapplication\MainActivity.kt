package com.example.myfirstapplication

import android.Manifest
import android.content.pm.PackageManager
import android.os.Bundle
import android.view.KeyEvent
import android.view.MotionEvent
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.ui.Modifier
import androidx.core.content.ContextCompat
import com.example.myfirstapplication.ui.screen.MainScreen
import com.example.myfirstapplication.ui.theme.MyFirstApplicationTheme
import com.example.myfirstapplication.viewmodel.MainViewModel

class MainActivity : ComponentActivity() {

    private val viewModel: MainViewModel by viewModels()

    // 权限请求启动器
    private val permissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        val allGranted = permissions.values.all { it }
        if (allGranted) {
            // 权限已授予，可以开始扫描WiFi
            viewModel.scanWiFiNetworks()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        // 检查并请求权限
        checkAndRequestPermissions()

        setContent {
            MyFirstApplicationTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    MainScreen(viewModel = viewModel)
                }
            }
        }
    }

    /**
     * 检查并请求必要权限
     */
    private fun checkAndRequestPermissions() {
        val permissions = arrayOf(
            Manifest.permission.ACCESS_FINE_LOCATION,
            Manifest.permission.ACCESS_COARSE_LOCATION,
            Manifest.permission.ACCESS_WIFI_STATE,
            Manifest.permission.CHANGE_WIFI_STATE,
            Manifest.permission.INTERNET,
            Manifest.permission.ACCESS_NETWORK_STATE
        )

        val permissionsToRequest = permissions.filter {
            ContextCompat.checkSelfPermission(this, it) != PackageManager.PERMISSION_GRANTED
        }

        if (permissionsToRequest.isNotEmpty()) {
            permissionLauncher.launch(permissionsToRequest.toTypedArray())
        } else {
            // 权限已授予
            viewModel.scanWiFiNetworks()
        }
    }

    /**
     * 处理手柄按键事件
     */
    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        return if (event != null && viewModel.handleGamepadKeyEvent(event)) {
            true
        } else {
            super.onKeyDown(keyCode, event)
        }
    }

    override fun onKeyUp(keyCode: Int, event: KeyEvent?): Boolean {
        return if (event != null && viewModel.handleGamepadKeyEvent(event)) {
            true
        } else {
            super.onKeyUp(keyCode, event)
        }
    }

    /**
     * 处理手柄摇杆事件
     */
    override fun onGenericMotionEvent(event: MotionEvent?): Boolean {
        return if (event != null && viewModel.handleGamepadMotionEvent(event)) {
            true
        } else {
            super.onGenericMotionEvent(event)
        }
    }

    override fun onResume() {
        super.onResume()
        // 重新扫描手柄
        viewModel.scanGamepads()
    }

    override fun onDestroy() {
        super.onDestroy()
        // 清理资源
        viewModel.disconnectRobotDog()
        viewModel.disconnectWiFi()
    }
}