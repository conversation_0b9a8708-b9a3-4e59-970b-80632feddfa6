package com.example.myfirstapplication.data.model

/**
 * 机器狗连接状态
 */
enum class ConnectionStatus {
    DISCONNECTED,
    CONNECTING,
    CONNECTED,
    ERROR
}

/**
 * 机器狗控制命令类型
 */
enum class CommandType {
    MOVE,           // 移动命令
    TURN,           // 转向命令
    STAND,          // 站立
    SIT,            // 坐下
    LIE_DOWN,       // 趴下
    DANCE,          // 跳舞
    FOLLOW,         // 跟随
    STOP,           // 停止
    CUSTOM          // 自定义命令
}

/**
 * 机器狗信息
 */
data class RobotDog(
    val id: String,
    val name: String,
    val ipAddress: String,
    val port: Int = 8080,
    val sshPort: Int = 22,
    val status: ConnectionStatus = ConnectionStatus.DISCONNECTED,
    val batteryLevel: Int = 0,
    val firmwareVersion: String = "",
    val lastConnected: Long = 0L
)

/**
 * 控制命令
 */
data class ControlCommand(
    val type: CommandType,
    val x: Float = 0f,      // X轴移动/转向值 (-1.0 到 1.0)
    val y: Float = 0f,      // Y轴移动值 (-1.0 到 1.0)
    val speed: Float = 0.5f, // 速度 (0.0 到 1.0)
    val duration: Long = 0L, // 持续时间(毫秒)
    val parameters: Map<String, Any> = emptyMap() // 额外参数
)

/**
 * 机器狗状态信息
 */
data class RobotStatus(
    val batteryLevel: Int,
    val temperature: Float,
    val position: Position,
    val orientation: Orientation,
    val isMoving: Boolean,
    val currentMode: String,
    val timestamp: Long = System.currentTimeMillis()
)

/**
 * 位置信息
 */
data class Position(
    val x: Float,
    val y: Float,
    val z: Float
)

/**
 * 方向信息
 */
data class Orientation(
    val roll: Float,
    val pitch: Float,
    val yaw: Float
)

/**
 * WiFi网络信息
 */
data class WiFiNetwork(
    val ssid: String,
    val bssid: String,
    val signalStrength: Int,
    val isSecured: Boolean,
    val frequency: Int
)

/**
 * 手柄输入状态
 */
data class GamepadInput(
    val leftStickX: Float = 0f,
    val leftStickY: Float = 0f,
    val rightStickX: Float = 0f,
    val rightStickY: Float = 0f,
    val leftTrigger: Float = 0f,
    val rightTrigger: Float = 0f,
    val buttonA: Boolean = false,
    val buttonB: Boolean = false,
    val buttonX: Boolean = false,
    val buttonY: Boolean = false,
    val buttonStart: Boolean = false,
    val buttonSelect: Boolean = false,
    val dPadUp: Boolean = false,
    val dPadDown: Boolean = false,
    val dPadLeft: Boolean = false,
    val dPadRight: Boolean = false
)
