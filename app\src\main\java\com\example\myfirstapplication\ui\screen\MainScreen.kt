package com.example.myfirstapplication.ui.screen

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.example.myfirstapplication.data.model.ConnectionStatus
import com.example.myfirstapplication.viewmodel.MainViewModel
import com.example.myfirstapplication.wifi.WiFiConnectionStatus

/**
 * 主屏幕
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainScreen(viewModel: MainViewModel) {
    var selectedTab by remember { mutableIntStateOf(0) }
    
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val connectionStatus by viewModel.connectionStatus.collectAsStateWithLifecycle()
    val robotStatus by viewModel.robotStatus.collectAsStateWithLifecycle()
    val wifiStatus by viewModel.wifiConnectionStatus.collectAsStateWithLifecycle()
    val isGamepadConnected by viewModel.isGamepadConnected.collectAsStateWithLifecycle()
    
    // 显示错误消息
    uiState.error?.let { error ->
        LaunchedEffect(error) {
            // 这里可以显示Snackbar或其他错误提示
        }
    }
    
    Column(modifier = Modifier.fillMaxSize()) {
        // 顶部状态栏
        StatusBar(
            connectionStatus = connectionStatus,
            wifiStatus = wifiStatus,
            isGamepadConnected = isGamepadConnected,
            batteryLevel = robotStatus?.batteryLevel ?: 0
        )
        
        // 主要内容区域
        Box(modifier = Modifier.weight(1f)) {
            when (selectedTab) {
                0 -> ConnectionScreen(viewModel)
                1 -> ControlScreen(viewModel)
                2 -> SettingsScreen(viewModel)
            }
            
            // 加载指示器
            if (uiState.isLoading) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            }
        }
        
        // 底部导航栏
        NavigationBar {
            NavigationBarItem(
                icon = { Icon(Icons.Default.Router, contentDescription = "Connection") },
                label = { Text("连接") },
                selected = selectedTab == 0,
                onClick = { selectedTab = 0 }
            )
            NavigationBarItem(
                icon = { Icon(Icons.Default.SportsEsports, contentDescription = "Control") },
                label = { Text("控制") },
                selected = selectedTab == 1,
                onClick = { selectedTab = 1 }
            )
            NavigationBarItem(
                icon = { Icon(Icons.Default.Settings, contentDescription = "Settings") },
                label = { Text("设置") },
                selected = selectedTab == 2,
                onClick = { selectedTab = 2 }
            )
        }
    }
}

/**
 * 状态栏
 */
@Composable
fun StatusBar(
    connectionStatus: ConnectionStatus,
    wifiStatus: WiFiConnectionStatus,
    isGamepadConnected: Boolean,
    batteryLevel: Int
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(8.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 机器狗连接状态
            StatusIndicator(
                icon = Icons.Default.Android,
                status = when (connectionStatus) {
                    ConnectionStatus.CONNECTED -> "已连接"
                    ConnectionStatus.CONNECTING -> "连接中"
                    ConnectionStatus.DISCONNECTED -> "未连接"
                    ConnectionStatus.ERROR -> "错误"
                },
                color = when (connectionStatus) {
                    ConnectionStatus.CONNECTED -> MaterialTheme.colorScheme.primary
                    ConnectionStatus.CONNECTING -> MaterialTheme.colorScheme.secondary
                    ConnectionStatus.DISCONNECTED -> MaterialTheme.colorScheme.outline
                    ConnectionStatus.ERROR -> MaterialTheme.colorScheme.error
                }
            )

            // WiFi状态
            StatusIndicator(
                icon = Icons.Default.Router,
                status = when (wifiStatus) {
                    WiFiConnectionStatus.CONNECTED -> "WiFi已连接"
                    WiFiConnectionStatus.CONNECTING -> "WiFi连接中"
                    WiFiConnectionStatus.SCANNING -> "扫描中"
                    WiFiConnectionStatus.DISCONNECTED -> "WiFi未连接"
                    WiFiConnectionStatus.FAILED -> "WiFi连接失败"
                },
                color = when (wifiStatus) {
                    WiFiConnectionStatus.CONNECTED -> MaterialTheme.colorScheme.primary
                    WiFiConnectionStatus.CONNECTING, WiFiConnectionStatus.SCANNING -> MaterialTheme.colorScheme.secondary
                    WiFiConnectionStatus.DISCONNECTED -> MaterialTheme.colorScheme.outline
                    WiFiConnectionStatus.FAILED -> MaterialTheme.colorScheme.error
                }
            )

            // 手柄状态
            StatusIndicator(
                icon = Icons.Default.SportsEsports,
                status = if (isGamepadConnected) "手柄已连接" else "手柄未连接",
                color = if (isGamepadConnected) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.outline
            )
            
            // 电池电量
            if (batteryLevel > 0) {
                StatusIndicator(
                    icon = when {
                        batteryLevel > 75 -> Icons.Default.Battery90
                        batteryLevel > 50 -> Icons.Default.Battery60
                        batteryLevel > 25 -> Icons.Default.Battery30
                        else -> Icons.Default.Battery20
                    },
                    status = "${batteryLevel}%",
                    color = when {
                        batteryLevel > 25 -> MaterialTheme.colorScheme.primary
                        else -> MaterialTheme.colorScheme.error
                    }
                )
            }
        }
    }
}

/**
 * 状态指示器
 */
@Composable
fun StatusIndicator(
    icon: ImageVector,
    status: String,
    color: androidx.compose.ui.graphics.Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            imageVector = icon,
            contentDescription = status,
            tint = color,
            modifier = Modifier.size(24.dp)
        )
        Text(
            text = status,
            style = MaterialTheme.typography.labelSmall,
            color = color
        )
    }
}
