#Thu Jul 10 19:03:15 CST 2025
com.example.myfirstapplication.app-main-60\:/drawable/ic_launcher_background.xml=D\:\\Users\\Administrator\\AndroidStudioProjects\\MyfirstApplication\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_launcher_background.xml.flat
com.example.myfirstapplication.app-main-60\:/drawable/ic_launcher_foreground.xml=D\:\\Users\\Administrator\\AndroidStudioProjects\\MyfirstApplication\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_launcher_foreground.xml.flat
com.example.myfirstapplication.app-main-60\:/mipmap-anydpi/ic_launcher.xml=D\:\\Users\\Administrator\\AndroidStudioProjects\\MyfirstApplication\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-anydpi_ic_launcher.xml.flat
com.example.myfirstapplication.app-main-60\:/mipmap-anydpi/ic_launcher_round.xml=D\:\\Users\\Administrator\\AndroidStudioProjects\\MyfirstApplication\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-anydpi_ic_launcher_round.xml.flat
com.example.myfirstapplication.app-main-60\:/mipmap-hdpi/ic_launcher.webp=D\:\\Users\\Administrator\\AndroidStudioProjects\\MyfirstApplication\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-hdpi_ic_launcher.webp.flat
com.example.myfirstapplication.app-main-60\:/mipmap-hdpi/ic_launcher_round.webp=D\:\\Users\\Administrator\\AndroidStudioProjects\\MyfirstApplication\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-hdpi_ic_launcher_round.webp.flat
com.example.myfirstapplication.app-main-60\:/mipmap-mdpi/ic_launcher.webp=D\:\\Users\\Administrator\\AndroidStudioProjects\\MyfirstApplication\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-mdpi_ic_launcher.webp.flat
com.example.myfirstapplication.app-main-60\:/mipmap-mdpi/ic_launcher_round.webp=D\:\\Users\\Administrator\\AndroidStudioProjects\\MyfirstApplication\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-mdpi_ic_launcher_round.webp.flat
com.example.myfirstapplication.app-main-60\:/mipmap-xhdpi/ic_launcher.webp=D\:\\Users\\Administrator\\AndroidStudioProjects\\MyfirstApplication\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xhdpi_ic_launcher.webp.flat
com.example.myfirstapplication.app-main-60\:/mipmap-xhdpi/ic_launcher_round.webp=D\:\\Users\\Administrator\\AndroidStudioProjects\\MyfirstApplication\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xhdpi_ic_launcher_round.webp.flat
com.example.myfirstapplication.app-main-60\:/mipmap-xxhdpi/ic_launcher.webp=D\:\\Users\\Administrator\\AndroidStudioProjects\\MyfirstApplication\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xxhdpi_ic_launcher.webp.flat
com.example.myfirstapplication.app-main-60\:/mipmap-xxhdpi/ic_launcher_round.webp=D\:\\Users\\Administrator\\AndroidStudioProjects\\MyfirstApplication\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xxhdpi_ic_launcher_round.webp.flat
com.example.myfirstapplication.app-main-60\:/mipmap-xxxhdpi/ic_launcher.webp=D\:\\Users\\Administrator\\AndroidStudioProjects\\MyfirstApplication\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xxxhdpi_ic_launcher.webp.flat
com.example.myfirstapplication.app-main-60\:/mipmap-xxxhdpi/ic_launcher_round.webp=D\:\\Users\\Administrator\\AndroidStudioProjects\\MyfirstApplication\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xxxhdpi_ic_launcher_round.webp.flat
com.example.myfirstapplication.app-main-60\:/xml/backup_rules.xml=D\:\\Users\\Administrator\\AndroidStudioProjects\\MyfirstApplication\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\xml_backup_rules.xml.flat
com.example.myfirstapplication.app-main-60\:/xml/data_extraction_rules.xml=D\:\\Users\\Administrator\\AndroidStudioProjects\\MyfirstApplication\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\xml_data_extraction_rules.xml.flat
