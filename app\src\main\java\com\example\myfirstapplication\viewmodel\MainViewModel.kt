package com.example.myfirstapplication.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.example.myfirstapplication.data.model.*
import com.example.myfirstapplication.gamepad.GamepadManager
import com.example.myfirstapplication.repository.RobotDogRepository
import com.example.myfirstapplication.ssh.SshManager
import com.example.myfirstapplication.wifi.WiFiManager
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch

/**
 * 主ViewModel
 */
class MainViewModel(application: Application) : AndroidViewModel(application) {
    
    private val repository = RobotDogRepository()
    private val wifiManager = WiFiManager(application)
    private val gamepadManager = GamepadManager()
    
    // UI状态
    private val _uiState = MutableStateFlow(MainUiState())
    val uiState: StateFlow<MainUiState> = _uiState.asStateFlow()
    
    // 机器狗连接状态
    val connectionStatus = repository.connectionStatus
    val robotStatus = repository.robotStatus
    val currentRobotDog = repository.currentRobotDog
    
    // WiFi状态
    val wifiConnectionStatus = wifiManager.connectionStatus
    val availableNetworks = wifiManager.availableNetworks
    val currentWifiNetwork = wifiManager.currentNetwork
    
    // 手柄状态
    val gamepadInput = gamepadManager.gamepadInput
    val isGamepadConnected = gamepadManager.isGamepadConnected
    
    init {
        // 初始化手柄扫描
        gamepadManager.scanForGamepads()
        
        // 监听手柄输入并自动发送控制命令
        viewModelScope.launch {
            gamepadInput.collect { input ->
                if (connectionStatus.value == ConnectionStatus.CONNECTED) {
                    val command = gamepadManager.gamepadInputToCommand(input)
                    command?.let { sendCommand(it) }
                }
            }
        }
    }
    
    /**
     * 连接到机器狗
     */
    fun connectToRobotDog(ipAddress: String, port: Int = 8080) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            
            val robotDog = RobotDog(
                id = "robot_${System.currentTimeMillis()}",
                name = "Lite3 Robot Dog",
                ipAddress = ipAddress,
                port = port
            )
            
            when (val result = repository.connectToRobotDog(robotDog)) {
                is NetworkResult.Success -> {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        message = "Successfully connected to robot dog"
                    )
                }
                is NetworkResult.Error -> {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = result.message
                    )
                }
                else -> {}
            }
        }
    }
    
    /**
     * 断开机器狗连接
     */
    fun disconnectRobotDog() {
        repository.disconnect()
        _uiState.value = _uiState.value.copy(
            message = "Disconnected from robot dog"
        )
    }
    
    /**
     * 发送控制命令
     */
    fun sendCommand(command: ControlCommand) {
        viewModelScope.launch {
            when (val result = repository.sendCommand(command)) {
                is NetworkResult.Error -> {
                    _uiState.value = _uiState.value.copy(
                        error = "Command failed: ${result.message}"
                    )
                }
                else -> {}
            }
        }
    }
    
    /**
     * 刷新机器狗状态
     */
    fun refreshRobotStatus() {
        viewModelScope.launch {
            repository.refreshRobotStatus()
        }
    }
    
    /**
     * 紧急停止
     */
    fun emergencyStop() {
        viewModelScope.launch {
            repository.emergencyStop()
        }
    }
    
    /**
     * 扫描WiFi网络
     */
    fun scanWiFiNetworks() {
        if (wifiManager.isWiFiEnabled()) {
            wifiManager.scanNetworks()
            // 延迟获取扫描结果
            viewModelScope.launch {
                kotlinx.coroutines.delay(3000)
                wifiManager.getScanResults()
            }
        } else {
            _uiState.value = _uiState.value.copy(
                error = "WiFi is not enabled"
            )
        }
    }
    
    /**
     * 连接到WiFi网络
     */
    fun connectToWiFi(ssid: String, password: String) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            
            val success = wifiManager.connectToNetwork(ssid, password)
            
            _uiState.value = _uiState.value.copy(
                isLoading = false,
                message = if (success) "Connecting to $ssid..." else "Failed to connect to $ssid"
            )
        }
    }
    
    /**
     * 断开WiFi连接
     */
    fun disconnectWiFi() {
        wifiManager.disconnect()
    }
    
    /**
     * 连接SSH
     */
    fun connectSsh(host: String, username: String, password: String, port: Int = 22) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            
            val success = repository.connectSsh(host, username, password, port)
            
            _uiState.value = _uiState.value.copy(
                isLoading = false,
                message = if (success) "SSH connected successfully" else "SSH connection failed"
            )
        }
    }
    
    /**
     * 执行SSH命令
     */
    fun executeSshCommand(command: String) {
        viewModelScope.launch {
            when (val result = repository.executeSshCommand(command)) {
                is SshManager.SshResult.Success -> {
                    _uiState.value = _uiState.value.copy(
                        sshOutput = result.output
                    )
                }
                is SshManager.SshResult.Error -> {
                    _uiState.value = _uiState.value.copy(
                        error = "SSH command failed: ${result.message}"
                    )
                }
            }
        }
    }
    
    /**
     * 清除错误消息
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
    
    /**
     * 清除普通消息
     */
    fun clearMessage() {
        _uiState.value = _uiState.value.copy(message = null)
    }
    
    /**
     * 处理手柄按键事件
     */
    fun handleGamepadKeyEvent(event: android.view.KeyEvent): Boolean {
        return gamepadManager.handleKeyEvent(event)
    }
    
    /**
     * 处理手柄摇杆事件
     */
    fun handleGamepadMotionEvent(event: android.view.MotionEvent): Boolean {
        return gamepadManager.handleMotionEvent(event)
    }
    
    /**
     * 扫描手柄
     */
    fun scanGamepads() {
        gamepadManager.scanForGamepads()
    }
    
    override fun onCleared() {
        super.onCleared()
        repository.disconnect()
        wifiManager.cleanup()
    }
}

/**
 * UI状态数据类
 */
data class MainUiState(
    val isLoading: Boolean = false,
    val error: String? = null,
    val message: String? = null,
    val sshOutput: String = ""
)
