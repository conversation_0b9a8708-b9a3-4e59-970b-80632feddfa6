1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.myfirstapplication"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="35"
9        android:targetSdkVersion="35" />
10
11    <!-- Network permissions -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:6:5-67
12-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:7:5-79
13-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
14-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:8:5-76
14-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:8:22-73
15    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
15-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:9:5-76
15-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:9:22-73
16    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
16-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:10:5-79
16-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:10:22-76
17
18    <!-- Location permission for WiFi scanning on Android 6+ -->
19    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
19-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:13:5-79
19-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:13:22-76
20    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
20-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:14:5-81
20-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:14:22-78
21
22    <!-- Wake lock for maintaining connection -->
23    <uses-permission android:name="android.permission.WAKE_LOCK" />
23-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:17:5-68
23-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:17:22-65
24
25    <!-- Vibration for haptic feedback -->
26    <uses-permission android:name="android.permission.VIBRATE" />
26-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:20:5-66
26-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:20:22-63
27    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
27-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:5-81
27-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:22-78
28    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
28-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:5-77
28-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:22-74
29
30    <permission
30-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee81e9003baef06be7850ff4f00325b8\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
31        android:name="com.example.myfirstapplication.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
31-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee81e9003baef06be7850ff4f00325b8\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
32        android:protectionLevel="signature" />
32-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee81e9003baef06be7850ff4f00325b8\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
33
34    <uses-permission android:name="com.example.myfirstapplication.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
34-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee81e9003baef06be7850ff4f00325b8\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
34-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee81e9003baef06be7850ff4f00325b8\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
35
36    <application
36-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:22:5-43:19
37        android:allowBackup="true"
37-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:23:9-35
38        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
38-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee81e9003baef06be7850ff4f00325b8\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
39        android:dataExtractionRules="@xml/data_extraction_rules"
39-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:24:9-65
40        android:extractNativeLibs="false"
41        android:fullBackupContent="@xml/backup_rules"
41-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:25:9-54
42        android:icon="@mipmap/ic_launcher"
42-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:26:9-43
43        android:label="@string/app_name"
43-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:27:9-41
44        android:roundIcon="@mipmap/ic_launcher_round"
44-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:28:9-54
45        android:supportsRtl="true"
45-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:29:9-35
46        android:theme="@style/Theme.MyFirstApplication" >
46-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:30:9-56
47        <activity
47-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:32:9-42:20
48            android:name="com.example.myfirstapplication.MainActivity"
48-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:33:13-41
49            android:exported="true"
49-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:34:13-36
50            android:label="@string/app_name"
50-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:35:13-45
51            android:theme="@style/Theme.MyFirstApplication" >
51-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:36:13-60
52            <intent-filter>
52-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:37:13-41:29
53                <action android:name="android.intent.action.MAIN" />
53-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:38:17-69
53-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:38:25-66
54
55                <category android:name="android.intent.category.LAUNCHER" />
55-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:40:17-77
55-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:40:27-74
56            </intent-filter>
57        </activity>
58
59        <provider
59-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:29:9-37:20
60            android:name="androidx.startup.InitializationProvider"
60-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:30:13-67
61            android:authorities="com.example.myfirstapplication.androidx-startup"
61-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:31:13-68
62            android:exported="false" >
62-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:32:13-37
63            <meta-data
63-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:34:13-36:52
64                android:name="androidx.work.WorkManagerInitializer"
64-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:35:17-68
65                android:value="androidx.startup" />
65-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:36:17-49
66            <meta-data
66-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37f27ceb4a61ff296c8d63ebd994f71d\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
67                android:name="androidx.emoji2.text.EmojiCompatInitializer"
67-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37f27ceb4a61ff296c8d63ebd994f71d\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
68                android:value="androidx.startup" />
68-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37f27ceb4a61ff296c8d63ebd994f71d\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
69            <meta-data
69-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\371a901e1b62b689ce41179655425bd7\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
70                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
70-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\371a901e1b62b689ce41179655425bd7\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
71                android:value="androidx.startup" />
71-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\371a901e1b62b689ce41179655425bd7\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
72            <meta-data
72-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
73                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
73-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
74                android:value="androidx.startup" />
74-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
75        </provider>
76
77        <service
77-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
78            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
78-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
79            android:directBootAware="false"
79-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
80            android:enabled="@bool/enable_system_alarm_service_default"
80-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
81            android:exported="false" />
81-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
82        <service
82-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
83            android:name="androidx.work.impl.background.systemjob.SystemJobService"
83-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
84            android:directBootAware="false"
84-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
85            android:enabled="@bool/enable_system_job_service_default"
85-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
86            android:exported="true"
86-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
87            android:permission="android.permission.BIND_JOB_SERVICE" />
87-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
88        <service
88-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
89            android:name="androidx.work.impl.foreground.SystemForegroundService"
89-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
90            android:directBootAware="false"
90-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
91            android:enabled="@bool/enable_system_foreground_service_default"
91-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
92            android:exported="false" />
92-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
93
94        <receiver
94-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
95            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
95-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
96            android:directBootAware="false"
96-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
97            android:enabled="true"
97-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
98            android:exported="false" />
98-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
99        <receiver
99-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
100            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
100-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
101            android:directBootAware="false"
101-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
102            android:enabled="false"
102-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
103            android:exported="false" >
103-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
104            <intent-filter>
104-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
105                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
105-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
105-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
106                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
106-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
106-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
107            </intent-filter>
108        </receiver>
109        <receiver
109-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
110            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
110-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
111            android:directBootAware="false"
111-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
112            android:enabled="false"
112-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
113            android:exported="false" >
113-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
114            <intent-filter>
114-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
115                <action android:name="android.intent.action.BATTERY_OKAY" />
115-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
115-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
116                <action android:name="android.intent.action.BATTERY_LOW" />
116-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
116-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
117            </intent-filter>
118        </receiver>
119        <receiver
119-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
120            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
120-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
121            android:directBootAware="false"
121-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
122            android:enabled="false"
122-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
123            android:exported="false" >
123-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
124            <intent-filter>
124-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
125                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
125-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
125-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
126                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
126-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
126-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
127            </intent-filter>
128        </receiver>
129        <receiver
129-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
130            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
130-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
131            android:directBootAware="false"
131-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
132            android:enabled="false"
132-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
133            android:exported="false" >
133-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
134            <intent-filter>
134-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
135                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
135-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
135-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
136            </intent-filter>
137        </receiver>
138        <receiver
138-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
139            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
139-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
140            android:directBootAware="false"
140-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
141            android:enabled="false"
141-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
142            android:exported="false" >
142-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
143            <intent-filter>
143-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
144                <action android:name="android.intent.action.BOOT_COMPLETED" />
144-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:17-79
144-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:25-76
145                <action android:name="android.intent.action.TIME_SET" />
145-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
145-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
146                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
146-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
146-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
147            </intent-filter>
148        </receiver>
149        <receiver
149-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
150            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
150-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
151            android:directBootAware="false"
151-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
152            android:enabled="@bool/enable_system_alarm_service_default"
152-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
153            android:exported="false" >
153-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
154            <intent-filter>
154-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
155                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
155-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
155-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
156            </intent-filter>
157        </receiver>
158        <receiver
158-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
159            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
159-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
160            android:directBootAware="false"
160-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
161            android:enabled="true"
161-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
162            android:exported="true"
162-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
163            android:permission="android.permission.DUMP" >
163-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
164            <intent-filter>
164-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
165                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
165-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
165-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
166            </intent-filter>
167        </receiver>
168
169        <service
169-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe83c34fd850f183f84d2b908d74f1c3\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
170            android:name="androidx.room.MultiInstanceInvalidationService"
170-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe83c34fd850f183f84d2b908d74f1c3\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
171            android:directBootAware="true"
171-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe83c34fd850f183f84d2b908d74f1c3\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
172            android:exported="false" />
172-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe83c34fd850f183f84d2b908d74f1c3\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
173
174        <receiver
174-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
175            android:name="androidx.profileinstaller.ProfileInstallReceiver"
175-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
176            android:directBootAware="false"
176-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
177            android:enabled="true"
177-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
178            android:exported="true"
178-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
179            android:permission="android.permission.DUMP" >
179-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
180            <intent-filter>
180-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
181                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
181-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
181-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
182            </intent-filter>
183            <intent-filter>
183-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
184                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
184-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
184-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
185            </intent-filter>
186            <intent-filter>
186-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
187                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
187-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
187-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
188            </intent-filter>
189            <intent-filter>
189-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
190                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
190-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
190-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
191            </intent-filter>
192        </receiver>
193    </application>
194
195</manifest>
