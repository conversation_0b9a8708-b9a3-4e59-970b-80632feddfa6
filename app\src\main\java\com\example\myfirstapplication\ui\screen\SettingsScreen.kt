package com.example.myfirstapplication.ui.screen

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.example.myfirstapplication.viewmodel.MainViewModel

/**
 * 设置屏幕
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsScreen(viewModel: MainViewModel) {
    var showSshDialog by remember { mutableStateOf(false) }
    var sshHost by remember { mutableStateOf("*************") }
    var sshUsername by remember { mutableStateOf("root") }
    var sshPassword by remember { mutableStateOf("") }
    var sshPort by remember { mutableStateOf("22") }
    
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val robotStatus by viewModel.robotStatus.collectAsStateWithLifecycle()
    val currentRobotDog by viewModel.currentRobotDog.collectAsStateWithLifecycle()
    
    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // SSH连接设置
        item {
            Card(
                modifier = Modifier.fillMaxWidth(),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "SSH远程连接",
                            style = MaterialTheme.typography.headlineSmall
                        )
                        
                        Button(
                            onClick = { showSshDialog = true }
                        ) {
                            Icon(Icons.Default.Terminal, contentDescription = null)
                            Spacer(modifier = Modifier.width(8.dp))
                            Text("连接SSH")
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Text(
                        text = "通过SSH连接到机器狗主机进行高级控制和调试",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    
                    if (uiState.sshOutput.isNotEmpty()) {
                        Spacer(modifier = Modifier.height(16.dp))
                        Card(
                            modifier = Modifier.fillMaxWidth(),
                            colors = CardDefaults.cardColors(
                                containerColor = MaterialTheme.colorScheme.surfaceVariant
                            )
                        ) {
                            Column(
                                modifier = Modifier.padding(12.dp)
                            ) {
                                Text(
                                    text = "SSH输出:",
                                    style = MaterialTheme.typography.labelMedium
                                )
                                Spacer(modifier = Modifier.height(4.dp))
                                Text(
                                    text = uiState.sshOutput,
                                    style = MaterialTheme.typography.bodySmall,
                                    fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace
                                )
                            }
                        }
                    }
                }
            }
        }
        
        // 机器狗信息
        item {
            Card(
                modifier = Modifier.fillMaxWidth(),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "机器狗信息",
                        style = MaterialTheme.typography.headlineSmall
                    )
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    currentRobotDog?.let { robot ->
                        InfoRow("名称", robot.name)
                        InfoRow("IP地址", robot.ipAddress)
                        InfoRow("端口", robot.port.toString())
                        InfoRow("SSH端口", robot.sshPort.toString())
                        InfoRow("固件版本", robot.firmwareVersion.ifEmpty { "未知" })
                    } ?: run {
                        Text(
                            text = "未连接到机器狗",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                    
                    robotStatus?.let { status ->
                        Spacer(modifier = Modifier.height(16.dp))
                        Divider()
                        Spacer(modifier = Modifier.height(16.dp))
                        
                        Text(
                            text = "实时状态",
                            style = MaterialTheme.typography.titleMedium
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        
                        InfoRow("电池电量", "${status.batteryLevel}%")
                        InfoRow("温度", "${status.temperature}°C")
                        InfoRow("当前模式", status.currentMode)
                        InfoRow("运动状态", if (status.isMoving) "运动中" else "静止")
                        InfoRow("位置", "(${String.format("%.2f", status.position.x)}, ${String.format("%.2f", status.position.y)}, ${String.format("%.2f", status.position.z)})")
                        InfoRow("方向", "Roll: ${String.format("%.1f", status.orientation.roll)}°, Pitch: ${String.format("%.1f", status.orientation.pitch)}°, Yaw: ${String.format("%.1f", status.orientation.yaw)}°")
                    }
                }
            }
        }
        
        // 控制设置
        item {
            Card(
                modifier = Modifier.fillMaxWidth(),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "控制设置",
                        style = MaterialTheme.typography.headlineSmall
                    )
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    // 刷新状态按钮
                    Button(
                        onClick = { viewModel.refreshRobotStatus() },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Icon(Icons.Default.Refresh, contentDescription = null)
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("刷新机器狗状态")
                    }
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    // 扫描手柄按钮
                    Button(
                        onClick = { viewModel.scanGamepads() },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Icon(Icons.Default.Gamepad, contentDescription = null)
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("扫描游戏手柄")
                    }
                }
            }
        }
        
        // 关于信息
        item {
            Card(
                modifier = Modifier.fillMaxWidth(),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "关于",
                        style = MaterialTheme.typography.headlineSmall
                    )
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    InfoRow("应用名称", "云深处Lite3控制器")
                    InfoRow("版本", "1.0.0")
                    InfoRow("开发者", "您的名字")
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    Text(
                        text = "这是一个用于控制云深处Lite3机器狗的Android应用程序。支持WiFi连接、手柄控制和SSH远程管理。",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
    
    // SSH连接对话框
    if (showSshDialog) {
        SshConnectDialog(
            host = sshHost,
            username = sshUsername,
            password = sshPassword,
            port = sshPort,
            onHostChange = { sshHost = it },
            onUsernameChange = { sshUsername = it },
            onPasswordChange = { sshPassword = it },
            onPortChange = { sshPort = it },
            onConnect = {
                val portInt = sshPort.toIntOrNull() ?: 22
                viewModel.connectSsh(sshHost, sshUsername, sshPassword, portInt)
                showSshDialog = false
            },
            onDismiss = { showSshDialog = false }
        )
    }
}

/**
 * 信息行
 */
@Composable
fun InfoRow(label: String, value: String) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodyMedium
        )
    }
}

/**
 * SSH连接对话框
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SshConnectDialog(
    host: String,
    username: String,
    password: String,
    port: String,
    onHostChange: (String) -> Unit,
    onUsernameChange: (String) -> Unit,
    onPasswordChange: (String) -> Unit,
    onPortChange: (String) -> Unit,
    onConnect: () -> Unit,
    onDismiss: () -> Unit
) {
    var showPassword by remember { mutableStateOf(false) }
    
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("SSH连接设置") },
        text = {
            Column(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                OutlinedTextField(
                    value = host,
                    onValueChange = onHostChange,
                    label = { Text("主机地址") },
                    modifier = Modifier.fillMaxWidth()
                )
                
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    OutlinedTextField(
                        value = username,
                        onValueChange = onUsernameChange,
                        label = { Text("用户名") },
                        modifier = Modifier.weight(1f)
                    )
                    
                    OutlinedTextField(
                        value = port,
                        onValueChange = onPortChange,
                        label = { Text("端口") },
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                        modifier = Modifier.weight(1f)
                    )
                }
                
                OutlinedTextField(
                    value = password,
                    onValueChange = onPasswordChange,
                    label = { Text("密码") },
                    visualTransformation = if (showPassword) VisualTransformation.None else PasswordVisualTransformation(),
                    trailingIcon = {
                        IconButton(onClick = { showPassword = !showPassword }) {
                            Icon(
                                imageVector = if (showPassword) Icons.Default.VisibilityOff else Icons.Default.Visibility,
                                contentDescription = if (showPassword) "隐藏密码" else "显示密码"
                            )
                        }
                    },
                    modifier = Modifier.fillMaxWidth()
                )
            }
        },
        confirmButton = {
            TextButton(
                onClick = onConnect,
                enabled = host.isNotBlank() && username.isNotBlank() && password.isNotBlank()
            ) {
                Text("连接")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}
