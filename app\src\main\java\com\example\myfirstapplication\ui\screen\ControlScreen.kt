package com.example.myfirstapplication.ui.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.example.myfirstapplication.data.model.CommandType
import com.example.myfirstapplication.data.model.ConnectionStatus
import com.example.myfirstapplication.data.model.ControlCommand
import com.example.myfirstapplication.viewmodel.MainViewModel
import kotlin.math.*

/**
 * 控制屏幕
 */
@Composable
fun ControlScreen(viewModel: MainViewModel) {
    val connectionStatus by viewModel.connectionStatus.collectAsStateWithLifecycle()
    val robotStatus by viewModel.robotStatus.collectAsStateWithLifecycle()
    val gamepadInput by viewModel.gamepadInput.collectAsStateWithLifecycle()
    val isGamepadConnected by viewModel.isGamepadConnected.collectAsStateWithLifecycle()
    
    val isConnected = connectionStatus == ConnectionStatus.CONNECTED
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 机器狗状态卡片
        robotStatus?.let { status ->
            Card(
                modifier = Modifier.fillMaxWidth(),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "机器狗状态",
                        style = MaterialTheme.typography.headlineSmall
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        StatusItem("电池", "${status.batteryLevel}%")
                        StatusItem("温度", "${status.temperature}°C")
                        StatusItem("模式", status.currentMode)
                        StatusItem("状态", if (status.isMoving) "运动中" else "静止")
                    }
                }
            }
        }
        
        // 手柄状态
        if (isGamepadConnected) {
            Card(
                modifier = Modifier.fillMaxWidth(),
                elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            Icons.Default.SportsEsports,
                            contentDescription = "手柄",
                            tint = MaterialTheme.colorScheme.primary
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = "手柄已连接",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.primary
                        )
                    }
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    // 显示手柄输入状态
                    Text(
                        text = "左摇杆: (${String.format("%.2f", gamepadInput.leftStickX)}, ${String.format("%.2f", gamepadInput.leftStickY)})",
                        style = MaterialTheme.typography.bodySmall
                    )
                    Text(
                        text = "右摇杆: (${String.format("%.2f", gamepadInput.rightStickX)}, ${String.format("%.2f", gamepadInput.rightStickY)})",
                        style = MaterialTheme.typography.bodySmall
                    )
                }
            }
        }
        
        // 虚拟控制面板
        Card(
            modifier = Modifier.fillMaxWidth(),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "虚拟控制面板",
                    style = MaterialTheme.typography.headlineSmall
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 控制按钮网格
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    // 左侧：虚拟摇杆
                    VirtualJoystick(
                        modifier = Modifier.size(120.dp),
                        enabled = isConnected,
                        onMove = { x, y ->
                            if (abs(x) > 0.1f || abs(y) > 0.1f) {
                                viewModel.sendCommand(
                                    ControlCommand(
                                        type = CommandType.MOVE,
                                        x = x,
                                        y = -y, // Y轴反转
                                        speed = 0.5f
                                    )
                                )
                            }
                        }
                    )
                    
                    // 右侧：动作按钮
                    Column(
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Row(
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            ControlButton(
                                icon = Icons.Default.Chair,
                                label = "坐下",
                                enabled = isConnected,
                                onClick = {
                                    viewModel.sendCommand(ControlCommand(type = CommandType.SIT))
                                }
                            )
                            ControlButton(
                                icon = Icons.Default.Person,
                                label = "站立",
                                enabled = isConnected,
                                onClick = {
                                    viewModel.sendCommand(ControlCommand(type = CommandType.STAND))
                                }
                            )
                        }
                        
                        Row(
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            ControlButton(
                                icon = Icons.Default.Bed,
                                label = "趴下",
                                enabled = isConnected,
                                onClick = {
                                    viewModel.sendCommand(ControlCommand(type = CommandType.LIE_DOWN))
                                }
                            )
                            ControlButton(
                                icon = Icons.Default.Music,
                                label = "跳舞",
                                enabled = isConnected,
                                onClick = {
                                    viewModel.sendCommand(ControlCommand(type = CommandType.DANCE))
                                }
                            )
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 紧急停止按钮
                Button(
                    onClick = { viewModel.emergencyStop() },
                    enabled = isConnected,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.error
                    ),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Icon(Icons.Default.PauseCircle, contentDescription = null)
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("紧急停止")
                }
            }
        }
        
        // 连接提示
        if (!isConnected) {
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.errorContainer
                )
            ) {
                Row(
                    modifier = Modifier.padding(16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        Icons.Default.Warning,
                        contentDescription = "警告",
                        tint = MaterialTheme.colorScheme.onErrorContainer
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "请先连接到机器狗才能进行控制",
                        color = MaterialTheme.colorScheme.onErrorContainer
                    )
                }
            }
        }
    }
}

/**
 * 状态项
 */
@Composable
fun StatusItem(label: String, value: String) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = value,
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.colorScheme.primary
        )
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

/**
 * 控制按钮
 */
@Composable
fun ControlButton(
    icon: ImageVector,
    label: String,
    enabled: Boolean,
    onClick: () -> Unit
) {
    Button(
        onClick = onClick,
        enabled = enabled,
        modifier = Modifier.size(60.dp),
        contentPadding = PaddingValues(4.dp)
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                icon,
                contentDescription = label,
                modifier = Modifier.size(20.dp)
            )
            Text(
                text = label,
                style = MaterialTheme.typography.labelSmall
            )
        }
    }
}

/**
 * 虚拟摇杆
 */
@Composable
fun VirtualJoystick(
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    onMove: (x: Float, y: Float) -> Unit
) {
    var centerPosition by remember { mutableStateOf(Offset.Zero) }
    var knobPosition by remember { mutableStateOf(Offset.Zero) }
    val density = LocalDensity.current
    
    Box(
        modifier = modifier
            .clip(CircleShape)
            .background(
                if (enabled) MaterialTheme.colorScheme.surfaceVariant
                else MaterialTheme.colorScheme.outline.copy(alpha = 0.3f)
            )
            .border(
                2.dp,
                if (enabled) MaterialTheme.colorScheme.outline
                else MaterialTheme.colorScheme.outline.copy(alpha = 0.3f),
                CircleShape
            )
            .pointerInput(enabled) {
                if (!enabled) return@pointerInput
                
                detectDragGestures(
                    onDragStart = { offset ->
                        centerPosition = Offset(size.width / 2f, size.height / 2f)
                        knobPosition = offset
                    },
                    onDragEnd = {
                        knobPosition = centerPosition
                        onMove(0f, 0f)
                    }
                ) { _, dragAmount ->
                    val newPosition = knobPosition + dragAmount
                    val distance = (newPosition - centerPosition).getDistance()
                    val maxDistance = size.width / 2f - with(density) { 20.dp.toPx() }
                    
                    knobPosition = if (distance <= maxDistance) {
                        newPosition
                    } else {
                        val angle = atan2(
                            newPosition.y - centerPosition.y,
                            newPosition.x - centerPosition.x
                        )
                        Offset(
                            centerPosition.x + cos(angle) * maxDistance,
                            centerPosition.y + sin(angle) * maxDistance
                        )
                    }
                    
                    val normalizedX = (knobPosition.x - centerPosition.x) / maxDistance
                    val normalizedY = (knobPosition.y - centerPosition.y) / maxDistance
                    onMove(normalizedX, normalizedY)
                }
            },
        contentAlignment = Alignment.Center
    ) {
        // 摇杆旋钮
        Box(
            modifier = Modifier
                .size(40.dp)
                .offset(
                    x = with(density) { (knobPosition.x - centerPosition.x).toDp() },
                    y = with(density) { (knobPosition.y - centerPosition.y).toDp() }
                )
                .clip(CircleShape)
                .background(
                    if (enabled) MaterialTheme.colorScheme.primary
                    else MaterialTheme.colorScheme.outline.copy(alpha = 0.5f)
                )
        )
    }
}
