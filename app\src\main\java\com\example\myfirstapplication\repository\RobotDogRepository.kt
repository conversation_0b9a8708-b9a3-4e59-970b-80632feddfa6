package com.example.myfirstapplication.repository

import com.example.myfirstapplication.data.model.*
import com.example.myfirstapplication.network.NetworkClient
import com.example.myfirstapplication.network.NetworkResult
import com.example.myfirstapplication.network.TcpClient
import com.example.myfirstapplication.ssh.SshManager
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * 机器狗数据仓库
 */
class RobotDogRepository {
    
    private val _connectionStatus = MutableStateFlow(ConnectionStatus.DISCONNECTED)
    val connectionStatus: StateFlow<ConnectionStatus> = _connectionStatus.asStateFlow()
    
    private val _robotStatus = MutableStateFlow<RobotStatus?>(null)
    val robotStatus: StateFlow<RobotStatus?> = _robotStatus.asStateFlow()
    
    private val _currentRobotDog = MutableStateFlow<RobotDog?>(null)
    val currentRobotDog: StateFlow<RobotDog?> = _currentRobotDog.asStateFlow()
    
    private var tcpClient: TcpClient? = null
    private val sshManager = SshManager()
    
    /**
     * 连接到机器狗
     */
    suspend fun connectToRobotDog(robotDog: RobotDog): NetworkResult<Unit> {
        return try {
            _connectionStatus.value = ConnectionStatus.CONNECTING
            
            // 设置网络客户端的基础URL
            NetworkClient.setBaseUrl("http://${robotDog.ipAddress}:${robotDog.port}")
            
            // 检查HTTP连接
            val isHttpConnected = NetworkClient.checkConnection()
            
            if (isHttpConnected) {
                // 创建TCP连接用于实时控制
                tcpClient = TcpClient(robotDog.ipAddress, robotDog.port + 1)
                val isTcpConnected = tcpClient?.connect() ?: false
                
                if (isTcpConnected) {
                    _currentRobotDog.value = robotDog.copy(status = ConnectionStatus.CONNECTED)
                    _connectionStatus.value = ConnectionStatus.CONNECTED
                    
                    // 获取初始状态
                    refreshRobotStatus()
                    
                    NetworkResult.Success(Unit)
                } else {
                    _connectionStatus.value = ConnectionStatus.ERROR
                    NetworkResult.Error("Failed to establish TCP connection")
                }
            } else {
                _connectionStatus.value = ConnectionStatus.ERROR
                NetworkResult.Error("Failed to connect to robot dog")
            }
        } catch (e: Exception) {
            _connectionStatus.value = ConnectionStatus.ERROR
            NetworkResult.Error("Connection failed: ${e.message}")
        }
    }
    
    /**
     * 断开连接
     */
    fun disconnect() {
        tcpClient?.disconnect()
        tcpClient = null
        sshManager.disconnect()
        _connectionStatus.value = ConnectionStatus.DISCONNECTED
        _currentRobotDog.value = null
        _robotStatus.value = null
    }
    
    /**
     * 发送控制命令
     */
    suspend fun sendCommand(command: ControlCommand): NetworkResult<Unit> {
        return try {
            if (_connectionStatus.value != ConnectionStatus.CONNECTED) {
                return NetworkResult.Error("Not connected to robot dog")
            }
            
            val response = NetworkClient.getApiService().sendCommand(command)
            if (response.isSuccessful) {
                NetworkResult.Success(Unit)
            } else {
                NetworkResult.Error("Command failed: ${response.code()}")
            }
        } catch (e: Exception) {
            NetworkResult.Error("Failed to send command: ${e.message}")
        }
    }
    
    /**
     * 刷新机器狗状态
     */
    suspend fun refreshRobotStatus(): NetworkResult<RobotStatus> {
        return try {
            if (_connectionStatus.value != ConnectionStatus.CONNECTED) {
                return NetworkResult.Error("Not connected to robot dog")
            }
            
            val response = NetworkClient.getApiService().getStatus()
            if (response.isSuccessful && response.body() != null) {
                val status = response.body()!!
                _robotStatus.value = status
                NetworkResult.Success(status)
            } else {
                NetworkResult.Error("Failed to get status: ${response.code()}")
            }
        } catch (e: Exception) {
            NetworkResult.Error("Failed to refresh status: ${e.message}")
        }
    }
    
    /**
     * 紧急停止
     */
    suspend fun emergencyStop(): NetworkResult<Unit> {
        return try {
            val response = NetworkClient.getApiService().emergencyStop()
            if (response.isSuccessful) {
                NetworkResult.Success(Unit)
            } else {
                NetworkResult.Error("Emergency stop failed: ${response.code()}")
            }
        } catch (e: Exception) {
            NetworkResult.Error("Emergency stop failed: ${e.message}")
        }
    }
    
    /**
     * 连接SSH
     */
    suspend fun connectSsh(
        host: String,
        username: String,
        password: String,
        port: Int = 22
    ): Boolean {
        return sshManager.connect(host, port, username, password)
    }
    
    /**
     * 执行SSH命令
     */
    suspend fun executeSshCommand(command: String): SshManager.SshResult {
        return sshManager.executeCommand(command)
    }
    
    /**
     * 断开SSH连接
     */
    fun disconnectSsh() {
        sshManager.disconnect()
    }
    
    /**
     * 检查SSH连接状态
     */
    fun isSshConnected(): Boolean {
        return sshManager.isConnected()
    }
    
    /**
     * 获取机器狗信息
     */
    suspend fun getRobotInfo(): NetworkResult<Map<String, Any>> {
        return try {
            if (_connectionStatus.value != ConnectionStatus.CONNECTED) {
                return NetworkResult.Error("Not connected to robot dog")
            }
            
            val response = NetworkClient.getApiService().getInfo()
            if (response.isSuccessful && response.body() != null) {
                NetworkResult.Success(response.body()!!)
            } else {
                NetworkResult.Error("Failed to get robot info: ${response.code()}")
            }
        } catch (e: Exception) {
            NetworkResult.Error("Failed to get robot info: ${e.message}")
        }
    }
    
    /**
     * 设置机器狗模式
     */
    suspend fun setRobotMode(mode: String): NetworkResult<Unit> {
        return try {
            val response = NetworkClient.getApiService().setMode(mapOf("mode" to mode))
            if (response.isSuccessful) {
                NetworkResult.Success(Unit)
            } else {
                NetworkResult.Error("Failed to set mode: ${response.code()}")
            }
        } catch (e: Exception) {
            NetworkResult.Error("Failed to set mode: ${e.message}")
        }
    }
}
