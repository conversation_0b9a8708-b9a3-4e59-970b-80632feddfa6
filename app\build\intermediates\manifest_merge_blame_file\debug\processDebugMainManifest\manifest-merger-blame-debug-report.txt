1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.myfirstapplication"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="35"
9        android:targetSdkVersion="35" />
10
11    <!-- Network permissions -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:6:5-67
12-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:7:5-79
13-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
14-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:8:5-76
14-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:8:22-73
15    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
15-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:9:5-76
15-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:9:22-73
16    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
16-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:10:5-79
16-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:10:22-76
17
18    <!-- Location permission for WiFi scanning on Android 6+ -->
19    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
19-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:13:5-79
19-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:13:22-76
20    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
20-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:14:5-81
20-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:14:22-78
21
22    <!-- Wake lock for maintaining connection -->
23    <uses-permission android:name="android.permission.WAKE_LOCK" />
23-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:17:5-68
23-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:17:22-65
24
25    <!-- Vibration for haptic feedback -->
26    <uses-permission android:name="android.permission.VIBRATE" />
26-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:20:5-66
26-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:20:22-63
27    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
27-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:5-81
27-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:22-78
28    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
28-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:5-77
28-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:22-74
29
30    <permission
30-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee81e9003baef06be7850ff4f00325b8\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
31        android:name="com.example.myfirstapplication.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
31-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee81e9003baef06be7850ff4f00325b8\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
32        android:protectionLevel="signature" />
32-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee81e9003baef06be7850ff4f00325b8\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
33
34    <uses-permission android:name="com.example.myfirstapplication.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
34-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee81e9003baef06be7850ff4f00325b8\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
34-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee81e9003baef06be7850ff4f00325b8\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
35
36    <application
36-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:22:5-43:19
37        android:allowBackup="true"
37-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:23:9-35
38        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
38-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee81e9003baef06be7850ff4f00325b8\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
39        android:dataExtractionRules="@xml/data_extraction_rules"
39-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:24:9-65
40        android:debuggable="true"
41        android:extractNativeLibs="false"
42        android:fullBackupContent="@xml/backup_rules"
42-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:25:9-54
43        android:icon="@mipmap/ic_launcher"
43-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:26:9-43
44        android:label="@string/app_name"
44-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:27:9-41
45        android:roundIcon="@mipmap/ic_launcher_round"
45-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:28:9-54
46        android:supportsRtl="true"
46-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:29:9-35
47        android:theme="@style/Theme.MyFirstApplication" >
47-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:30:9-56
48        <activity
48-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:32:9-42:20
49            android:name="com.example.myfirstapplication.MainActivity"
49-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:33:13-41
50            android:exported="true"
50-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:34:13-36
51            android:label="@string/app_name"
51-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:35:13-45
52            android:theme="@style/Theme.MyFirstApplication" >
52-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:36:13-60
53            <intent-filter>
53-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:37:13-41:29
54                <action android:name="android.intent.action.MAIN" />
54-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:38:17-69
54-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:38:25-66
55
56                <category android:name="android.intent.category.LAUNCHER" />
56-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:40:17-77
56-->D:\Users\Administrator\AndroidStudioProjects\MyfirstApplication\app\src\main\AndroidManifest.xml:40:27-74
57            </intent-filter>
58        </activity>
59
60        <provider
60-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:29:9-37:20
61            android:name="androidx.startup.InitializationProvider"
61-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:30:13-67
62            android:authorities="com.example.myfirstapplication.androidx-startup"
62-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:31:13-68
63            android:exported="false" >
63-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:32:13-37
64            <meta-data
64-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:34:13-36:52
65                android:name="androidx.work.WorkManagerInitializer"
65-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:35:17-68
66                android:value="androidx.startup" />
66-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:36:17-49
67            <meta-data
67-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37f27ceb4a61ff296c8d63ebd994f71d\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
68                android:name="androidx.emoji2.text.EmojiCompatInitializer"
68-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37f27ceb4a61ff296c8d63ebd994f71d\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
69                android:value="androidx.startup" />
69-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37f27ceb4a61ff296c8d63ebd994f71d\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
70            <meta-data
70-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\371a901e1b62b689ce41179655425bd7\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
71                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
71-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\371a901e1b62b689ce41179655425bd7\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
72                android:value="androidx.startup" />
72-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\371a901e1b62b689ce41179655425bd7\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
73            <meta-data
73-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
74                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
74-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
75                android:value="androidx.startup" />
75-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
76        </provider>
77
78        <service
78-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
79            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
79-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
80            android:directBootAware="false"
80-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
81            android:enabled="@bool/enable_system_alarm_service_default"
81-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
82            android:exported="false" />
82-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
83        <service
83-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
84            android:name="androidx.work.impl.background.systemjob.SystemJobService"
84-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
85            android:directBootAware="false"
85-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
86            android:enabled="@bool/enable_system_job_service_default"
86-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
87            android:exported="true"
87-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
88            android:permission="android.permission.BIND_JOB_SERVICE" />
88-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
89        <service
89-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
90            android:name="androidx.work.impl.foreground.SystemForegroundService"
90-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
91            android:directBootAware="false"
91-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
92            android:enabled="@bool/enable_system_foreground_service_default"
92-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
93            android:exported="false" />
93-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
94
95        <receiver
95-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
96            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
96-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
97            android:directBootAware="false"
97-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
98            android:enabled="true"
98-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
99            android:exported="false" />
99-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
100        <receiver
100-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
101            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
101-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
102            android:directBootAware="false"
102-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
103            android:enabled="false"
103-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
104            android:exported="false" >
104-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
105            <intent-filter>
105-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
106                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
106-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
106-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
107                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
107-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
107-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
108            </intent-filter>
109        </receiver>
110        <receiver
110-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
111            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
111-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
112            android:directBootAware="false"
112-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
113            android:enabled="false"
113-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
114            android:exported="false" >
114-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
115            <intent-filter>
115-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
116                <action android:name="android.intent.action.BATTERY_OKAY" />
116-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
116-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
117                <action android:name="android.intent.action.BATTERY_LOW" />
117-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
117-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
118            </intent-filter>
119        </receiver>
120        <receiver
120-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
121            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
121-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
122            android:directBootAware="false"
122-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
123            android:enabled="false"
123-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
124            android:exported="false" >
124-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
125            <intent-filter>
125-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
126                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
126-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
126-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
127                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
127-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
127-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
128            </intent-filter>
129        </receiver>
130        <receiver
130-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
131            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
131-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
132            android:directBootAware="false"
132-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
133            android:enabled="false"
133-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
134            android:exported="false" >
134-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
135            <intent-filter>
135-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
136                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
136-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
136-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
137            </intent-filter>
138        </receiver>
139        <receiver
139-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
140            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
140-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
141            android:directBootAware="false"
141-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
142            android:enabled="false"
142-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
143            android:exported="false" >
143-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
144            <intent-filter>
144-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
145                <action android:name="android.intent.action.BOOT_COMPLETED" />
145-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:17-79
145-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:25-76
146                <action android:name="android.intent.action.TIME_SET" />
146-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
146-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
147                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
147-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
147-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
148            </intent-filter>
149        </receiver>
150        <receiver
150-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
151            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
151-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
152            android:directBootAware="false"
152-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
153            android:enabled="@bool/enable_system_alarm_service_default"
153-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
154            android:exported="false" >
154-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
155            <intent-filter>
155-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
156                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
156-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
156-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
157            </intent-filter>
158        </receiver>
159        <receiver
159-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
160            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
160-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
161            android:directBootAware="false"
161-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
162            android:enabled="true"
162-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
163            android:exported="true"
163-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
164            android:permission="android.permission.DUMP" >
164-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
165            <intent-filter>
165-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
166                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
166-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
166-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ff2cc660253f8dbb77cb0cbe74e61c\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
167            </intent-filter>
168        </receiver>
169
170        <activity
170-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f38b5c402d23b30c2e2875c7c76f10e0\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
171            android:name="androidx.compose.ui.tooling.PreviewActivity"
171-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f38b5c402d23b30c2e2875c7c76f10e0\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
172            android:exported="true" />
172-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f38b5c402d23b30c2e2875c7c76f10e0\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
173        <activity
173-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf0789ae4c448878dc74378ebdfb6958\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:23:9-25:39
174            android:name="androidx.activity.ComponentActivity"
174-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf0789ae4c448878dc74378ebdfb6958\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:24:13-63
175            android:exported="true" />
175-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf0789ae4c448878dc74378ebdfb6958\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:25:13-36
176
177        <service
177-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe83c34fd850f183f84d2b908d74f1c3\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
178            android:name="androidx.room.MultiInstanceInvalidationService"
178-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe83c34fd850f183f84d2b908d74f1c3\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
179            android:directBootAware="true"
179-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe83c34fd850f183f84d2b908d74f1c3\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
180            android:exported="false" />
180-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe83c34fd850f183f84d2b908d74f1c3\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
181
182        <receiver
182-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
183            android:name="androidx.profileinstaller.ProfileInstallReceiver"
183-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
184            android:directBootAware="false"
184-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
185            android:enabled="true"
185-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
186            android:exported="true"
186-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
187            android:permission="android.permission.DUMP" >
187-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
188            <intent-filter>
188-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
189                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
189-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
189-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
190            </intent-filter>
191            <intent-filter>
191-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
192                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
192-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
192-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
193            </intent-filter>
194            <intent-filter>
194-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
195                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
195-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
195-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
196            </intent-filter>
197            <intent-filter>
197-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
198                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
198-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
198-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
199            </intent-filter>
200        </receiver>
201    </application>
202
203</manifest>
