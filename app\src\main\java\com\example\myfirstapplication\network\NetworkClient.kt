package com.example.myfirstapplication.network

import com.google.gson.GsonBuilder
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit

/**
 * 网络客户端单例
 */
object NetworkClient {
    
    private var baseUrl: String = "http://192.168.1.100:8080/"
    
    private val loggingInterceptor = HttpLoggingInterceptor().apply {
        level = HttpLoggingInterceptor.Level.BODY
    }
    
    private val okHttpClient = OkHttpClient.Builder()
        .addInterceptor(loggingInterceptor)
        .connectTimeout(10, TimeUnit.SECONDS)
        .readTimeout(30, TimeUnit.SECONDS)
        .writeTimeout(30, TimeUnit.SECONDS)
        .build()
    
    private val gson = GsonBuilder()
        .setLenient()
        .create()
    
    private var retrofit: Retrofit? = null
    
    /**
     * 设置基础URL
     */
    fun setBaseUrl(url: String) {
        baseUrl = if (url.endsWith("/")) url else "$url/"
        retrofit = null // 重置retrofit实例
    }
    
    /**
     * 获取Retrofit实例
     */
    private fun getRetrofit(): Retrofit {
        if (retrofit == null) {
            retrofit = Retrofit.Builder()
                .baseUrl(baseUrl)
                .client(okHttpClient)
                .addConverterFactory(GsonConverterFactory.create(gson))
                .build()
        }
        return retrofit!!
    }
    
    /**
     * 获取API服务
     */
    fun getApiService(): RobotDogApi {
        return getRetrofit().create(RobotDogApi::class.java)
    }
    
    /**
     * 检查网络连接
     */
    suspend fun checkConnection(): Boolean {
        return try {
            val response = getApiService().getStatus()
            response.isSuccessful
        } catch (e: Exception) {
            false
        }
    }
}

/**
 * TCP客户端用于实时控制
 */
class TcpClient(private val host: String, private val port: Int) {
    
    private var socket: java.net.Socket? = null
    private var isConnected = false
    
    /**
     * 连接到机器狗
     */
    suspend fun connect(): Boolean {
        return try {
            socket = java.net.Socket(host, port)
            isConnected = true
            true
        } catch (e: Exception) {
            isConnected = false
            false
        }
    }
    
    /**
     * 断开连接
     */
    fun disconnect() {
        try {
            socket?.close()
        } catch (e: Exception) {
            // 忽略关闭异常
        } finally {
            socket = null
            isConnected = false
        }
    }
    
    /**
     * 发送数据
     */
    suspend fun sendData(data: ByteArray): Boolean {
        return try {
            if (isConnected && socket != null) {
                socket!!.getOutputStream().write(data)
                socket!!.getOutputStream().flush()
                true
            } else {
                false
            }
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 接收数据
     */
    suspend fun receiveData(): ByteArray? {
        return try {
            if (isConnected && socket != null) {
                val buffer = ByteArray(1024)
                val bytesRead = socket!!.getInputStream().read(buffer)
                if (bytesRead > 0) {
                    buffer.copyOf(bytesRead)
                } else {
                    null
                }
            } else {
                null
            }
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * 检查连接状态
     */
    fun isConnected(): Boolean {
        return isConnected && socket?.isConnected == true && !socket!!.isClosed
    }
}
