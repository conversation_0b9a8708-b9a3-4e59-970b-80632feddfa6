package com.example.myfirstapplication.ui.screen

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.example.myfirstapplication.data.model.ConnectionStatus
import com.example.myfirstapplication.data.model.WiFiNetwork
import com.example.myfirstapplication.viewmodel.MainViewModel
import com.example.myfirstapplication.wifi.WiFiConnectionStatus

/**
 * 连接屏幕
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ConnectionScreen(viewModel: MainViewModel) {
    var robotIpAddress by remember { mutableStateOf("*************") }
    var robotPort by remember { mutableStateOf("8080") }
    var showWifiDialog by remember { mutableStateOf(false) }
    var selectedWifiNetwork by remember { mutableStateOf<WiFiNetwork?>(null) }
    
    val connectionStatus by viewModel.connectionStatus.collectAsStateWithLifecycle()
    val wifiStatus by viewModel.wifiConnectionStatus.collectAsStateWithLifecycle()
    val availableNetworks by viewModel.availableNetworks.collectAsStateWithLifecycle()
    val currentWifiNetwork by viewModel.currentWifiNetwork.collectAsStateWithLifecycle()
    
    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 机器狗连接部分
        item {
            Card(
                modifier = Modifier.fillMaxWidth(),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "机器狗连接",
                        style = MaterialTheme.typography.headlineSmall
                    )
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    OutlinedTextField(
                        value = robotIpAddress,
                        onValueChange = { robotIpAddress = it },
                        label = { Text("IP地址") },
                        modifier = Modifier.fillMaxWidth(),
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number)
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    OutlinedTextField(
                        value = robotPort,
                        onValueChange = { robotPort = it },
                        label = { Text("端口") },
                        modifier = Modifier.fillMaxWidth(),
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number)
                    )
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Button(
                            onClick = {
                                val port = robotPort.toIntOrNull() ?: 8080
                                viewModel.connectToRobotDog(robotIpAddress, port)
                            },
                            enabled = connectionStatus != ConnectionStatus.CONNECTING,
                            modifier = Modifier.weight(1f)
                        ) {
                            if (connectionStatus == ConnectionStatus.CONNECTING) {
                                CircularProgressIndicator(
                                    modifier = Modifier.size(16.dp),
                                    strokeWidth = 2.dp
                                )
                            } else {
                                Icon(Icons.Default.Link, contentDescription = null)
                            }
                            Spacer(modifier = Modifier.width(8.dp))
                            Text("连接")
                        }
                        
                        if (connectionStatus == ConnectionStatus.CONNECTED) {
                            Button(
                                onClick = { viewModel.disconnectRobotDog() },
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = MaterialTheme.colorScheme.error
                                ),
                                modifier = Modifier.weight(1f)
                            ) {
                                Icon(Icons.Default.LinkOff, contentDescription = null)
                                Spacer(modifier = Modifier.width(8.dp))
                                Text("断开")
                            }
                        }
                    }
                    
                    // 连接状态显示
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = when (connectionStatus) {
                            ConnectionStatus.CONNECTED -> "✅ 已连接到机器狗"
                            ConnectionStatus.CONNECTING -> "🔄 正在连接..."
                            ConnectionStatus.DISCONNECTED -> "❌ 未连接"
                            ConnectionStatus.ERROR -> "⚠️ 连接错误"
                        },
                        style = MaterialTheme.typography.bodyMedium,
                        color = when (connectionStatus) {
                            ConnectionStatus.CONNECTED -> MaterialTheme.colorScheme.primary
                            ConnectionStatus.ERROR -> MaterialTheme.colorScheme.error
                            else -> MaterialTheme.colorScheme.onSurface
                        }
                    )
                }
            }
        }
        
        // WiFi连接部分
        item {
            Card(
                modifier = Modifier.fillMaxWidth(),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "WiFi网络",
                            style = MaterialTheme.typography.headlineSmall
                        )
                        
                        IconButton(
                            onClick = { viewModel.scanWiFiNetworks() }
                        ) {
                            Icon(Icons.Default.Refresh, contentDescription = "刷新")
                        }
                    }
                    
                    // 当前连接的网络
                    currentWifiNetwork?.let { network ->
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "当前连接: ${network.ssid}",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.primary
                        )
                    }
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    Text(
                        text = "可用网络:",
                        style = MaterialTheme.typography.bodyLarge
                    )
                }
            }
        }
        
        // WiFi网络列表
        if (wifiStatus == WiFiConnectionStatus.SCANNING) {
            item {
                Box(
                    modifier = Modifier.fillMaxWidth(),
                    contentAlignment = Alignment.Center
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        CircularProgressIndicator(modifier = Modifier.size(16.dp))
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("扫描WiFi网络中...")
                    }
                }
            }
        }
        
        items(availableNetworks) { network ->
            WiFiNetworkItem(
                network = network,
                onClick = {
                    selectedWifiNetwork = network
                    showWifiDialog = true
                }
            )
        }
    }
    
    // WiFi连接对话框
    if (showWifiDialog && selectedWifiNetwork != null) {
        WiFiConnectDialog(
            network = selectedWifiNetwork!!,
            onConnect = { password ->
                viewModel.connectToWiFi(selectedWifiNetwork!!.ssid, password)
                showWifiDialog = false
                selectedWifiNetwork = null
            },
            onDismiss = {
                showWifiDialog = false
                selectedWifiNetwork = null
            }
        )
    }
}

/**
 * WiFi网络项
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun WiFiNetworkItem(
    network: WiFiNetwork,
    onClick: () -> Unit
) {
    Card(
        onClick = onClick,
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = network.ssid,
                    style = MaterialTheme.typography.bodyLarge
                )
                Text(
                    text = if (network.isSecured) "安全网络" else "开放网络",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 信号强度图标
                Icon(
                    imageVector = when (network.signalStrength) {
                        in 4..5 -> Icons.Default.SignalCellular4Bar
                        3 -> Icons.Default.SignalCellular3Bar
                        2 -> Icons.Default.SignalCellular2Bar
                        1 -> Icons.Default.SignalCellular1Bar
                        else -> Icons.Default.SignalCellular0Bar
                    },
                    contentDescription = "信号强度",
                    tint = MaterialTheme.colorScheme.onSurfaceVariant
                )
                
                if (network.isSecured) {
                    Spacer(modifier = Modifier.width(8.dp))
                    Icon(
                        imageVector = Icons.Default.Lock,
                        contentDescription = "安全网络",
                        tint = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.size(16.dp)
                    )
                }
            }
        }
    }
}

/**
 * WiFi连接对话框
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun WiFiConnectDialog(
    network: WiFiNetwork,
    onConnect: (String) -> Unit,
    onDismiss: () -> Unit
) {
    var password by remember { mutableStateOf("") }
    var showPassword by remember { mutableStateOf(false) }
    
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("连接到 ${network.ssid}") },
        text = {
            Column {
                if (network.isSecured) {
                    OutlinedTextField(
                        value = password,
                        onValueChange = { password = it },
                        label = { Text("密码") },
                        visualTransformation = if (showPassword) VisualTransformation.None else PasswordVisualTransformation(),
                        trailingIcon = {
                            IconButton(onClick = { showPassword = !showPassword }) {
                                Icon(
                                    imageVector = if (showPassword) Icons.Default.VisibilityOff else Icons.Default.Visibility,
                                    contentDescription = if (showPassword) "隐藏密码" else "显示密码"
                                )
                            }
                        },
                        modifier = Modifier.fillMaxWidth()
                    )
                } else {
                    Text("这是一个开放网络，无需密码。")
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = { onConnect(password) },
                enabled = !network.isSecured || password.isNotBlank()
            ) {
                Text("连接")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}
