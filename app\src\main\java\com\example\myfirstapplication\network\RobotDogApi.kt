package com.example.myfirstapplication.network

import com.example.myfirstapplication.data.model.ControlCommand
import com.example.myfirstapplication.data.model.RobotStatus
import retrofit2.Response
import retrofit2.http.*

/**
 * 机器狗API接口
 */
interface RobotDogApi {
    
    /**
     * 获取机器狗状态
     */
    @GET("status")
    suspend fun getStatus(): Response<RobotStatus>
    
    /**
     * 发送控制命令
     */
    @POST("command")
    suspend fun sendCommand(@Body command: ControlCommand): Response<Unit>
    
    /**
     * 获取机器狗信息
     */
    @GET("info")
    suspend fun getInfo(): Response<Map<String, Any>>
    
    /**
     * 设置机器狗模式
     */
    @POST("mode")
    suspend fun setMode(@Body mode: Map<String, String>): Response<Unit>
    
    /**
     * 紧急停止
     */
    @POST("emergency_stop")
    suspend fun emergencyStop(): Response<Unit>
    
    /**
     * 获取电池信息
     */
    @GET("battery")
    suspend fun getBatteryInfo(): Response<Map<String, Any>>
    
    /**
     * 校准机器狗
     */
    @POST("calibrate")
    suspend fun calibrate(): Response<Unit>
    
    /**
     * 重启机器狗
     */
    @POST("reboot")
    suspend fun reboot(): Response<Unit>
}

/**
 * WebSocket消息类型
 */
sealed class WebSocketMessage {
    data class StatusUpdate(val status: RobotStatus) : WebSocketMessage()
    data class CommandResponse(val success: Boolean, val message: String) : WebSocketMessage()
    data class Error(val error: String) : WebSocketMessage()
    data class BatteryUpdate(val level: Int) : WebSocketMessage()
}

/**
 * 网络响应包装类
 */
sealed class NetworkResult<T> {
    data class Success<T>(val data: T) : NetworkResult<T>()
    data class Error<T>(val message: String, val code: Int? = null) : NetworkResult<T>()
    data class Loading<T>(val isLoading: Boolean = true) : NetworkResult<T>()
}
